import pandas as pd
import openpyxl
from datetime import datetime
import os

class LotusDataReader:
    def __init__(self, excel_file_path):
        """
        初始化Lotus数据读取器
        
        Args:
            excel_file_path (str): Excel文件路径
        """
        self.excel_file_path = excel_file_path
        self.monthly_data = None
        self.sheet1_data = None
        self.matched_data = []
        
    def read_monthly_sheet(self):
        """读取Monthly工作表的数据"""
        try:
            print("正在读取Monthly工作表...")
            self.monthly_data = pd.read_excel(
                self.excel_file_path, 
                sheet_name='Monthly'
            )
            print(f"Monthly工作表读取成功，共 {len(self.monthly_data)} 行数据")
            
            # 显示列名以便调试
            print("Monthly工作表的列名:")
            for i, col in enumerate(self.monthly_data.columns):
                print(f"  {i}: {col}")
                
            return True
            
        except Exception as e:
            print(f"读取Monthly工作表时出错: {str(e)}")
            return False
    
    def read_sheet1(self):
        """读取Sheet1工作表的数据"""
        try:
            print("正在读取Sheet1工作表...")
            self.sheet1_data = pd.read_excel(
                self.excel_file_path, 
                sheet_name='Sheet1'
            )
            print(f"Sheet1工作表读取成功，共 {len(self.sheet1_data)} 行数据")
            
            # 显示列名以便调试
            print("Sheet1工作表的列名:")
            for i, col in enumerate(self.sheet1_data.columns):
                print(f"  {i}: {col}")
                
            return True
            
        except Exception as e:
            print(f"读取Sheet1工作表时出错: {str(e)}")
            return False
    
    def get_available_sheets(self):
        """获取Excel文件中所有可用的工作表名称"""
        try:
            xl_file = pd.ExcelFile(self.excel_file_path)
            sheets = xl_file.sheet_names
            print("可用的工作表:")
            for i, sheet in enumerate(sheets):
                print(f"  {i}: {sheet}")
            return sheets
        except Exception as e:
            print(f"获取工作表列表时出错: {str(e)}")
            return []
    
    def preview_data(self, sheet_name, rows=5):
        """预览指定工作表的前几行数据"""
        try:
            data = pd.read_excel(self.excel_file_path, sheet_name=sheet_name)
            print(f"\n{sheet_name} 工作表预览 (前{rows}行):")
            print("=" * 80)
            print(data.head(rows).to_string())
            print("=" * 80)
            return data
        except Exception as e:
            print(f"预览 {sheet_name} 工作表时出错: {str(e)}")
            return None
    
    def match_data(self):
        """
        匹配Monthly和Sheet1的数据
        匹配条件: STORE NAME + TRANSACTION DATE + Item code
        """
        if self.monthly_data is None or self.sheet1_data is None:
            print("请先读取两个工作表的数据")
            return False
        
        print("开始匹配数据...")
        
        # 清理和准备数据
        monthly_clean = self.monthly_data.copy()
        sheet1_clean = self.sheet1_data.copy()
        
        # 创建匹配键 (需要根据实际列名调整)
        try:
            # 假设的列名，需要根据实际情况调整
            monthly_key_cols = ['STORE NAME', 'TRANSACTION DATE', 'ITEM(TPNA)']
            sheet1_key_cols = ['STORE NAME', 'TRANSACTION DATE', 'Item code']
            
            # 检查列是否存在
            missing_monthly = [col for col in monthly_key_cols if col not in monthly_clean.columns]
            missing_sheet1 = [col for col in sheet1_key_cols if col not in sheet1_clean.columns]
            
            if missing_monthly:
                print(f"Monthly工作表缺少列: {missing_monthly}")
                return False
            
            if missing_sheet1:
                print(f"Sheet1工作表缺少列: {missing_sheet1}")
                return False
            
            # 创建匹配键
            monthly_clean['match_key'] = (
                monthly_clean['STORE NAME'].astype(str) + '|' +
                monthly_clean['TRANSACTION DATE'].astype(str) + '|' +
                monthly_clean['ITEM(TPNA)'].astype(str)
            )
            
            sheet1_clean['match_key'] = (
                sheet1_clean['STORE NAME'].astype(str) + '|' +
                sheet1_clean['TRANSACTION DATE'].astype(str) + '|' +
                sheet1_clean['Item code'].astype(str)
            )
            
            # 执行匹配
            matched = pd.merge(
                monthly_clean,
                sheet1_clean[['match_key', 'Promotion Retail (RM)']],
                on='match_key',
                how='left'
            )
            
            self.matched_data = matched
            print(f"匹配完成，共 {len(matched)} 行数据")
            print(f"成功匹配 {matched['Promotion Retail (RM)'].notna().sum()} 行")
            
            return True
            
        except Exception as e:
            print(f"匹配数据时出错: {str(e)}")
            return False
    
    def get_lotus_item_codes(self):
        """获取去重后的Lotus Item Code列表"""
        if self.matched_data is None or len(self.matched_data) == 0:
            print("没有匹配的数据")
            return []
        
        # 获取有效的item codes (非空且匹配成功的)
        valid_data = self.matched_data[
            (self.matched_data['ITEM(TPNA)'].notna()) & 
            (self.matched_data['Promotion Retail (RM)'].notna())
        ]
        
        # 去重
        unique_items = valid_data['ITEM(TPNA)'].unique()
        
        print(f"找到 {len(unique_items)} 个唯一的Lotus Item Code")
        
        return unique_items.tolist()
    
    def export_matched_data(self, output_file="matched_lotus_data.xlsx"):
        """导出匹配后的数据"""
        if self.matched_data is None or len(self.matched_data) == 0:
            print("没有数据可导出")
            return False
        
        try:
            self.matched_data.to_excel(output_file, index=False)
            print(f"匹配数据已导出到: {output_file}")
            return True
        except Exception as e:
            print(f"导出数据时出错: {str(e)}")
            return False

# 使用示例
def main():
    # 检查文件是否存在
    excel_file = "lotus generate new.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"错误: 找不到文件 '{excel_file}'")
        print("请确保文件在当前目录中")
        return
    
    # 创建读取器
    reader = LotusDataReader(excel_file)
    
    # 获取可用工作表
    sheets = reader.get_available_sheets()
    
    # 预览数据
    if 'Monthly' in sheets:
        reader.preview_data('Monthly', 3)
    
    if 'Sheet1' in sheets:
        reader.preview_data('Sheet1', 3)
    
    # 读取数据
    if reader.read_monthly_sheet() and reader.read_sheet1():
        # 匹配数据
        if reader.match_data():
            # 获取Lotus Item Codes
            item_codes = reader.get_lotus_item_codes()
            print(f"\n获取到的Lotus Item Codes (前10个):")
            for i, code in enumerate(item_codes[:10]):
                print(f"  {i+1}: {code}")
            
            # 导出匹配数据
            reader.export_matched_data()

if __name__ == "__main__":
    main()
