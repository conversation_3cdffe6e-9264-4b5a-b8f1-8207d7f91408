#!/usr/bin/env python3
"""
测试Lotus数据读取器
"""

import os
import glob
from lotus_data_reader import LotusDataReader

def find_excel_files():
    """查找当前目录中的Excel文件"""
    excel_patterns = ["*.xlsx", "*.xls"]
    excel_files = []
    
    for pattern in excel_patterns:
        excel_files.extend(glob.glob(pattern))
    
    return excel_files

def main():
    print("=== Lotus数据读取器测试 ===\n")
    
    # 查找Excel文件
    excel_files = find_excel_files()
    
    if not excel_files:
        print("❌ 当前目录中没有找到Excel文件")
        print("请将 'lotus generate new.xlsx' 文件放到当前目录中")
        return
    
    print("📁 找到的Excel文件:")
    for i, file in enumerate(excel_files):
        size = os.path.getsize(file)
        print(f"  {i+1}: {file} ({size} bytes)")
    
    # 查找目标文件
    target_file = None
    for file in excel_files:
        if "lotus" in file.lower() and "generate" in file.lower():
            target_file = file
            break
    
    if not target_file:
        print(f"\n⚠️  没有找到 'lotus generate new.xlsx' 文件")
        print("可用的Excel文件:")
        for i, file in enumerate(excel_files):
            print(f"  {i+1}: {file}")
        
        # 让用户选择文件
        try:
            choice = input(f"\n请选择要使用的文件 (1-{len(excel_files)}) 或按Enter跳过: ")
            if choice.strip():
                idx = int(choice) - 1
                if 0 <= idx < len(excel_files):
                    target_file = excel_files[idx]
                else:
                    print("无效选择")
                    return
            else:
                return
        except (ValueError, KeyboardInterrupt):
            print("操作取消")
            return
    
    print(f"\n🎯 使用文件: {target_file}")
    
    # 创建读取器并测试
    try:
        reader = LotusDataReader(target_file)
        
        # 获取工作表列表
        print(f"\n📊 分析文件: {target_file}")
        sheets = reader.get_available_sheets()
        
        if not sheets:
            print("❌ 无法读取工作表")
            return
        
        # 检查必需的工作表
        has_monthly = any("monthly" in sheet.lower() for sheet in sheets)
        has_sheet1 = any("sheet1" in sheet.lower() for sheet in sheets)
        
        print(f"\n✅ 工作表检查:")
        print(f"  Monthly工作表: {'✓' if has_monthly else '✗'}")
        print(f"  Sheet1工作表: {'✓' if has_sheet1 else '✗'}")
        
        # 预览数据
        if has_monthly:
            monthly_sheet = next(sheet for sheet in sheets if "monthly" in sheet.lower())
            print(f"\n📋 预览 {monthly_sheet} 工作表:")
            reader.preview_data(monthly_sheet, 3)
        
        if has_sheet1:
            sheet1_name = next(sheet for sheet in sheets if "sheet1" in sheet.lower())
            print(f"\n📋 预览 {sheet1_name} 工作表:")
            reader.preview_data(sheet1_name, 3)
        
        # 如果两个工作表都存在，尝试处理数据
        if has_monthly and has_sheet1:
            print(f"\n🔄 开始处理数据...")
            
            if reader.read_monthly_sheet() and reader.read_sheet1():
                print("✅ 数据读取成功")
                
                # 尝试匹配数据
                if reader.match_data():
                    print("✅ 数据匹配成功")
                    
                    # 获取item codes
                    item_codes = reader.get_lotus_item_codes()
                    if item_codes:
                        print(f"\n🎯 成功获取 {len(item_codes)} 个唯一的Lotus Item Code")
                        print("前10个Item Code:")
                        for i, code in enumerate(item_codes[:10]):
                            print(f"  {i+1}: {code}")
                        
                        # 导出数据
                        reader.export_matched_data()
                    else:
                        print("⚠️  没有找到有效的Item Code")
                else:
                    print("❌ 数据匹配失败")
            else:
                print("❌ 数据读取失败")
        else:
            print("\n⚠️  缺少必需的工作表，无法继续处理")
            
    except Exception as e:
        print(f"❌ 处理文件时出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
