#!/usr/bin/env python3
"""
运行Excel生成器的简单脚本
"""

from excel_generator import ExcelGenerator
import os

def main():
    print("开始生成Excel文件...")
    
    try:
        # 创建Excel生成器实例
        generator = ExcelGenerator()
        
        # 生成Excel文件
        filename = generator.generate_excel("sales_report.xlsx")
        
        # 检查文件是否成功创建
        if os.path.exists(filename):
            file_size = os.path.getsize(filename)
            print(f"✅ Excel文件生成成功!")
            print(f"📁 文件名: {filename}")
            print(f"📊 文件大小: {file_size} bytes")
            print(f"📍 文件路径: {os.path.abspath(filename)}")
        else:
            print("❌ 文件生成失败")
            
    except Exception as e:
        print(f"❌ 生成Excel文件时出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
