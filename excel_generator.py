import openpyxl
from openpyxl.styles import <PERSON><PERSON><PERSON><PERSON>, Font, Alignment, Border, Side
from openpyxl.utils import get_column_letter
import pandas as pd
from datetime import datetime

class ExcelGenerator:
    def __init__(self):
        self.workbook = openpyxl.Workbook()
        self.worksheet = self.workbook.active
        self.worksheet.title = "Sales Report"
        
    def setup_headers(self):
        """设置Excel表头，所有列都在第一行"""
        
        # 所有表头都在第一行，按照新的顺序排列
        headers = [
            ("A1", "STORE CODE"),
            ("B1", "LOCATION NAME"),
            ("C1", "LOCATION CODE"),
            ("D1", "TRANSACTION DATE"),
            ("E1", "LOTUS ITEM CODE"),
            ("F1", "SKU"),
            ("G1", "ITEM(TPNA) DESCRIPTION"),
            ("H1", "LOTUS UNIT PRICE"),
            ("I1", "ED UNIT PRICE"),
            ("J1", "ED MARGIN"),
            ("K1", "UNIT VAR"),
            ("L1", "TOTAL VAR"),
            ("M1", "POST-MARGIN VAR"),
            ("N1", "QTY"),
            ("O1", "NET SALES EX VAT"),
            ("P1", "REV"),
            ("Q1", "ITEM TYPE")
        ]
        
        # 设置表头内容
        for cell_ref, header_text in headers:
            cell = self.worksheet[cell_ref]
            cell.value = header_text
            
        # 设置表头样式
        self.format_headers()
        
    def format_headers(self):
        """格式化表头样式"""
        # 表头样式
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_font = Font(color="FFFFFF", bold=True, size=11)
        header_alignment = Alignment(horizontal="center", vertical="center")
        
        # 边框样式
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 应用样式到表头行
        for col in range(1, 18):  # A到Q列
            cell = self.worksheet.cell(row=1, column=col)
            cell.fill = header_fill
            cell.font = header_font
            cell.alignment = header_alignment
            cell.border = thin_border
            
    def add_sample_data(self):
        """添加示例数据"""
        sample_data = [
            ["ST001", "Central Store", "LOC001", "2024-01-15", "LT001", "SKU001", "Product A", 10.50, 12.00, 1.50, 0.25, 5.00, 0.75, 20, 210.00, 240.00, "Regular"],
            ["ST002", "North Branch", "LOC002", "2024-01-15", "LT002", "SKU002", "Product B", 25.00, 28.00, 3.00, 0.50, 10.00, 1.50, 15, 375.00, 420.00, "Premium"],
            ["ST003", "South Store", "LOC003", "2024-01-16", "LT003", "SKU003", "Product C", 8.75, 10.00, 1.25, 0.15, 3.00, 0.45, 30, 262.50, 300.00, "Regular"],
            ["ST001", "Central Store", "LOC001", "2024-01-16", "LT004", "SKU004", "Product D", 15.00, 18.00, 3.00, 0.75, 15.00, 2.25, 25, 375.00, 450.00, "Premium"],
            ["ST004", "East Branch", "LOC004", "2024-01-17", "LT005", "SKU005", "Product E", 12.25, 14.50, 2.25, 0.35, 7.00, 1.05, 18, 220.50, 261.00, "Regular"]
        ]
        
        # 添加数据到工作表
        for row_idx, row_data in enumerate(sample_data, start=2):
            for col_idx, value in enumerate(row_data, start=1):
                cell = self.worksheet.cell(row=row_idx, column=col_idx)
                cell.value = value
                
                # 为数据行添加边框
                thin_border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
                cell.border = thin_border
                
                # 数字格式化
                if col_idx in [8, 9, 10, 11, 12, 13, 15, 16]:  # 价格和数量列
                    if isinstance(value, (int, float)):
                        if col_idx == 14:  # QTY列
                            cell.number_format = '0'
                        else:
                            cell.number_format = '0.00'
                            
    def adjust_column_widths(self):
        """调整列宽"""
        column_widths = {
            'A': 12,  # STORE CODE
            'B': 20,  # LOCATION NAME
            'C': 15,  # LOCATION CODE
            'D': 18,  # TRANSACTION DATE
            'E': 18,  # LOTUS ITEM CODE
            'F': 12,  # SKU
            'G': 25,  # ITEM DESCRIPTION
            'H': 18,  # LOTUS UNIT PRICE
            'I': 15,  # ED UNIT PRICE
            'J': 12,  # ED MARGIN
            'K': 12,  # UNIT VAR
            'L': 12,  # TOTAL VAR
            'M': 18,  # POST-MARGIN VAR
            'N': 8,   # QTY
            'O': 18,  # NET SALES EX VAT
            'P': 12,  # REV
            'Q': 12   # ITEM TYPE
        }
        
        for column, width in column_widths.items():
            self.worksheet.column_dimensions[column].width = width
            
    def add_summary_row(self):
        """添加汇总行"""
        last_row = self.worksheet.max_row + 1
        
        # 添加汇总标签
        summary_cell = self.worksheet.cell(row=last_row, column=1)
        summary_cell.value = "TOTAL"
        summary_cell.font = Font(bold=True)
        
        # 添加汇总公式
        qty_sum = self.worksheet.cell(row=last_row, column=14)
        qty_sum.value = f"=SUM(N2:N{last_row-1})"
        qty_sum.font = Font(bold=True)
        
        net_sales_sum = self.worksheet.cell(row=last_row, column=15)
        net_sales_sum.value = f"=SUM(O2:O{last_row-1})"
        net_sales_sum.font = Font(bold=True)
        net_sales_sum.number_format = '0.00'
        
        rev_sum = self.worksheet.cell(row=last_row, column=16)
        rev_sum.value = f"=SUM(P2:P{last_row-1})"
        rev_sum.font = Font(bold=True)
        rev_sum.number_format = '0.00'
        
    def generate_excel(self, filename="sales_report.xlsx"):
        """生成完整的Excel文件"""
        # 设置表头
        self.setup_headers()
        
        # 添加示例数据
        self.add_sample_data()
        
        # 调整列宽
        self.adjust_column_widths()
        
        # 添加汇总行
        self.add_summary_row()
        
        # 保存文件
        self.workbook.save(filename)
        print(f"Excel文件已生成: {filename}")
        
        return filename

# 使用示例
if __name__ == "__main__":
    generator = ExcelGenerator()
    filename = generator.generate_excel("sales_report.xlsx")
    print(f"Excel文件已成功生成: {filename}")
