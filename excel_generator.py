import openpyxl
from openpyxl.styles import <PERSON><PERSON><PERSON><PERSON>, Font, Alignment, Border, Side
from openpyxl.utils import get_column_letter
import pandas as pd
from datetime import datetime
import os
import glob

class ExcelGenerator:
    def __init__(self):
        self.workbook = openpyxl.Workbook()
        self.worksheet = self.workbook.active
        self.worksheet.title = "Sales Report"

        # Lotus数据相关属性
        self.lotus_excel_path = None
        self.monthly_data = None
        self.sheet1_data = None
        self.matched_data = []
        self.lotus_item_codes = []
        
    def setup_headers(self):
        """设置Excel表头，所有列都在第一行"""
        
        # 所有表头都在第一行，按照新的顺序排列
        headers = [
            ("A1", "STORE CODE"),
            ("B1", "LOCATION NAME"),
            ("C1", "LOCATION CODE"),
            ("D1", "TRANSACTION DATE"),
            ("E1", "LOTUS ITEM CODE"),
            ("F1", "SKU"),
            ("G1", "ITEM(TPNA) DESCRIPTION"),
            ("H1", "LOTUS UNIT PRICE"),
            ("I1", "ED UNIT PRICE"),
            ("J1", "ED MARGIN"),
            ("K1", "UNIT VAR"),
            ("L1", "TOTAL VAR"),
            ("M1", "POST-MARGIN VAR"),
            ("N1", "QTY"),
            ("O1", "NET SALES EX VAT"),
            ("P1", "REV"),
            ("Q1", "ITEM TYPE")
        ]
        
        # 设置表头内容
        for cell_ref, header_text in headers:
            cell = self.worksheet[cell_ref]
            cell.value = header_text
            
        # 设置表头样式
        self.format_headers()
        
    def format_headers(self):
        """格式化表头样式"""
        # 表头样式
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_font = Font(color="FFFFFF", bold=True, size=11)
        header_alignment = Alignment(horizontal="center", vertical="center")
        
        # 边框样式
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 应用样式到表头行
        for col in range(1, 18):  # A到Q列
            cell = self.worksheet.cell(row=1, column=col)
            cell.fill = header_fill
            cell.font = header_font
            cell.alignment = header_alignment
            cell.border = thin_border
            
    def add_data_row(self, row_data):
        """添加单行数据"""
        next_row = self.worksheet.max_row + 1

        for col_idx, value in enumerate(row_data, start=1):
            cell = self.worksheet.cell(row=next_row, column=col_idx)
            cell.value = value

            # 为数据行添加边框
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            cell.border = thin_border

            # 数字格式化
            if col_idx in [8, 9, 10, 11, 12, 13, 15, 16]:  # 价格和数量列
                if isinstance(value, (int, float)):
                    if col_idx == 14:  # QTY列
                        cell.number_format = '0'
                    else:
                        cell.number_format = '0.00'
                            
    def adjust_column_widths(self):
        """调整列宽"""
        column_widths = {
            'A': 12,  # STORE CODE
            'B': 20,  # LOCATION NAME
            'C': 15,  # LOCATION CODE
            'D': 18,  # TRANSACTION DATE
            'E': 18,  # LOTUS ITEM CODE
            'F': 12,  # SKU
            'G': 25,  # ITEM DESCRIPTION
            'H': 18,  # LOTUS UNIT PRICE
            'I': 15,  # ED UNIT PRICE
            'J': 12,  # ED MARGIN
            'K': 12,  # UNIT VAR
            'L': 12,  # TOTAL VAR
            'M': 18,  # POST-MARGIN VAR
            'N': 8,   # QTY
            'O': 18,  # NET SALES EX VAT
            'P': 12,  # REV
            'Q': 12   # ITEM TYPE
        }
        
        for column, width in column_widths.items():
            self.worksheet.column_dimensions[column].width = width
            
    def add_summary_row(self):
        """添加汇总行（仅在有数据时调用）"""
        if self.worksheet.max_row <= 1:  # 只有表头，没有数据
            return

        last_row = self.worksheet.max_row + 1

        # 添加汇总标签
        summary_cell = self.worksheet.cell(row=last_row, column=1)
        summary_cell.value = "TOTAL"
        summary_cell.font = Font(bold=True)

        # 添加汇总公式
        qty_sum = self.worksheet.cell(row=last_row, column=14)
        qty_sum.value = f"=SUM(N2:N{last_row-1})"
        qty_sum.font = Font(bold=True)

        net_sales_sum = self.worksheet.cell(row=last_row, column=15)
        net_sales_sum.value = f"=SUM(O2:O{last_row-1})"
        net_sales_sum.font = Font(bold=True)
        net_sales_sum.number_format = '0.00'

        rev_sum = self.worksheet.cell(row=last_row, column=16)
        rev_sum.value = f"=SUM(P2:P{last_row-1})"
        rev_sum.font = Font(bold=True)
        rev_sum.number_format = '0.00'
        
    def generate_excel(self, filename="sales_report.xlsx", add_summary=False):
        """生成Excel文件（仅包含表头）"""
        # 设置表头
        self.setup_headers()

        # 调整列宽
        self.adjust_column_widths()

        # 如果需要，添加汇总行
        if add_summary:
            self.add_summary_row()

        # 保存文件
        self.workbook.save(filename)
        print(f"Excel文件已生成: {filename}")

        return filename

    # ===== Lotus数据读取功能 =====

    def find_lotus_excel(self):
        """查找lotus excel文件"""
        excel_patterns = ["*.xlsx", "*.xls"]
        excel_files = []

        for pattern in excel_patterns:
            excel_files.extend(glob.glob(pattern))

        # 查找包含lotus和generate的文件
        for file in excel_files:
            if "lotus" in file.lower() and "generate" in file.lower():
                self.lotus_excel_path = file
                print(f"找到Lotus文件: {file}")
                return True

        print("可用的Excel文件:")
        for i, file in enumerate(excel_files):
            print(f"  {i+1}: {file}")

        return False

    def read_lotus_data(self, excel_file=None):
        """读取Lotus Excel数据"""
        if excel_file:
            self.lotus_excel_path = excel_file
        elif not self.lotus_excel_path:
            if not self.find_lotus_excel():
                print("未找到Lotus Excel文件")
                return False

        try:
            # 获取工作表列表
            xl_file = pd.ExcelFile(self.lotus_excel_path)
            sheets = xl_file.sheet_names
            print(f"工作表: {sheets}")

            # 读取Monthly工作表
            monthly_sheet = None
            for sheet in sheets:
                if "monthly" in sheet.lower():
                    monthly_sheet = sheet
                    break

            if monthly_sheet:
                self.monthly_data = pd.read_excel(self.lotus_excel_path, sheet_name=monthly_sheet)
                print(f"Monthly工作表读取成功: {len(self.monthly_data)} 行")
                print("Monthly列名:", list(self.monthly_data.columns))

            # 读取Sheet1工作表
            sheet1_name = None
            for sheet in sheets:
                if "sheet1" in sheet.lower():
                    sheet1_name = sheet
                    break

            if sheet1_name:
                self.sheet1_data = pd.read_excel(self.lotus_excel_path, sheet_name=sheet1_name)
                print(f"Sheet1工作表读取成功: {len(self.sheet1_data)} 行")
                print("Sheet1列名:", list(self.sheet1_data.columns))

            return True

        except Exception as e:
            print(f"读取Lotus数据出错: {str(e)}")
            return False

    def match_lotus_data(self):
        """匹配Monthly和Sheet1数据"""
        if self.monthly_data is None or self.sheet1_data is None:
            print("请先读取Lotus数据")
            return False

        try:
            # 创建匹配键
            monthly_clean = self.monthly_data.copy()
            sheet1_clean = self.sheet1_data.copy()

            # 检查必需的列
            required_monthly = ['STORE NAME', 'TRANSACTION DATE', 'ITEM(TPNA)']
            required_sheet1 = ['STORE NAME', 'TRANSACTION DATE', 'Item code', 'Promotion Retail (RM)']

            # 检查列是否存在
            missing_monthly = [col for col in required_monthly if col not in monthly_clean.columns]
            missing_sheet1 = [col for col in required_sheet1 if col not in sheet1_clean.columns]

            if missing_monthly:
                print(f"Monthly工作表缺少列: {missing_monthly}")
                print("可用列:", list(monthly_clean.columns))
                return False

            if missing_sheet1:
                print(f"Sheet1工作表缺少列: {missing_sheet1}")
                print("可用列:", list(sheet1_clean.columns))
                return False

            # 创建匹配键
            monthly_clean['match_key'] = (
                monthly_clean['STORE NAME'].astype(str) + '|' +
                monthly_clean['TRANSACTION DATE'].astype(str) + '|' +
                monthly_clean['ITEM(TPNA)'].astype(str)
            )

            sheet1_clean['match_key'] = (
                sheet1_clean['STORE NAME'].astype(str) + '|' +
                sheet1_clean['TRANSACTION DATE'].astype(str) + '|' +
                sheet1_clean['Item code'].astype(str)
            )

            # 执行匹配
            matched = pd.merge(
                monthly_clean,
                sheet1_clean[['match_key', 'Promotion Retail (RM)']],
                on='match_key',
                how='left'
            )

            self.matched_data = matched

            # 获取去重的item codes
            valid_data = matched[
                (matched['ITEM(TPNA)'].notna()) &
                (matched['Promotion Retail (RM)'].notna())
            ]

            self.lotus_item_codes = valid_data['ITEM(TPNA)'].unique().tolist()

            print(f"匹配完成: {len(matched)} 行数据")
            print(f"成功匹配: {matched['Promotion Retail (RM)'].notna().sum()} 行")
            print(f"唯一Item Code: {len(self.lotus_item_codes)} 个")

            return True

        except Exception as e:
            print(f"匹配数据出错: {str(e)}")
            return False

    def get_lotus_item_codes(self):
        """获取Lotus Item Codes"""
        return self.lotus_item_codes

    def process_lotus_data(self, excel_file=None):
        """完整处理Lotus数据的流程"""
        print("=== 开始处理Lotus数据 ===")

        if self.read_lotus_data(excel_file):
            if self.match_lotus_data():
                print(f"✅ 成功获取 {len(self.lotus_item_codes)} 个Lotus Item Code")

                # 显示所有item codes
                print("\n📋 所有Lotus Item Codes:")
                for i, code in enumerate(self.lotus_item_codes, 1):
                    print(f"  {i}: {code}")

                return True

        print("❌ Lotus数据处理失败")
        return False

    def export_lotus_item_codes(self, filename="lotus_item_codes.txt"):
        """导出所有item codes到文本文件"""
        if not self.lotus_item_codes:
            print("没有item codes可导出")
            return False

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"Lotus Item Codes ({len(self.lotus_item_codes)} 个)\n")
                f.write("=" * 50 + "\n")
                for i, code in enumerate(self.lotus_item_codes, 1):
                    f.write(f"{i}: {code}\n")

            print(f"✅ Item codes已导出到: {filename}")
            return True

        except Exception as e:
            print(f"导出失败: {str(e)}")
            return False

# 使用示例
if __name__ == "__main__":
    generator = ExcelGenerator()

    # 处理Lotus数据
    if generator.process_lotus_data():
        item_codes = generator.get_lotus_item_codes()
        print(f"\n📊 总共获取到 {len(item_codes)} 个唯一的Lotus Item Code")

        # 导出item codes到文本文件
        generator.export_lotus_item_codes()

    # 生成Excel文件
    filename = generator.generate_excel("sales_report.xlsx")
    print(f"\n📁 Excel文件已成功生成: {filename}")
